"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginLoggerUtil = void 0;
const user_login_log_entity_1 = require("../../util/database/mysql/user_login_log/entities/user_login_log.entity");
class LoginLoggerUtil {
    static userLoginLogService;
    static ipLocationUtil;
    static riskAssessmentUtil;
    static setService(service) {
        console.log('🔧 设置 LoginLoggerUtil 服务实例:', {
            hasService: !!service,
            serviceType: service?.constructor?.name
        });
        LoginLoggerUtil.userLoginLogService = service;
        console.log('✅ LoginLoggerUtil 服务实例设置完成');
    }
    static setIpLocationService(ipLocationUtil, riskAssessmentUtil) {
        console.log('🌍 设置 IP地理位置服务实例:', {
            hasIpLocationUtil: !!ipLocationUtil,
            hasRiskAssessmentUtil: !!riskAssessmentUtil
        });
        LoginLoggerUtil.ipLocationUtil = ipLocationUtil;
        LoginLoggerUtil.riskAssessmentUtil = riskAssessmentUtil;
        console.log('✅ IP地理位置服务实例设置完成');
    }
    static async logSuccessLogin(data) {
        const startTime = Date.now();
        const logData = {
            timestamp: new Date().toISOString(),
            level: 'INFO',
            event: 'LOGIN_SUCCESS',
            ...data,
            userAgent: data.userAgent ? `${data.userAgent.substring(0, 100)}...` : '未知设备'
        };
        console.log('✅ [LOGIN_SUCCESS]', JSON.stringify(logData, null, 2));
        let locationInfo = null;
        let riskAssessment = null;
        if (data.enableLocationResolution !== false && data.clientIp && LoginLoggerUtil.ipLocationUtil) {
            try {
                console.log('🌍 开始解析IP地理位置:', LoginLoggerUtil.maskIP(data.clientIp));
                locationInfo = await LoginLoggerUtil.ipLocationUtil.getLocationByIP(data.clientIp);
                console.log('✅ IP地理位置解析完成:', {
                    location: locationInfo.displayName,
                    confidence: locationInfo.confidence,
                    dataSource: locationInfo.dataSource
                });
                if (LoginLoggerUtil.riskAssessmentUtil) {
                    try {
                        const userHistory = {
                            commonLocations: [],
                            riskLoginCount: 0,
                            totalLoginCount: 1
                        };
                        const risk = await LoginLoggerUtil.riskAssessmentUtil.assessLoginRisk(data.userId, locationInfo, userHistory);
                        riskAssessment = {
                            level: risk.level,
                            reason: risk.reason
                        };
                        console.log('🔍 风险评估完成:', {
                            level: risk.level,
                            score: risk.score,
                            reason: risk.reason
                        });
                    }
                    catch (riskError) {
                        console.warn('⚠️ 风险评估失败:', riskError.message);
                        riskAssessment = {
                            level: user_login_log_entity_1.RiskLevel.LOW,
                            reason: '风险评估异常，默认低风险'
                        };
                    }
                }
            }
            catch (locationError) {
                console.warn('⚠️ IP地理位置解析失败:', locationError.message);
                locationInfo = {
                    country: '未知',
                    province: '未知',
                    city: '未知',
                    isp: '未知',
                    dataSource: 'fallback',
                    hasEmptyFields: true,
                    confidence: 0,
                    displayName: '未知位置'
                };
            }
        }
        try {
            console.log('🔍 检查服务实例:', {
                hasService: !!LoginLoggerUtil.userLoginLogService,
                serviceType: LoginLoggerUtil.userLoginLogService?.constructor?.name
            });
            if (LoginLoggerUtil.userLoginLogService) {
                console.log('📝 开始保存登录成功日志到数据库...');
                const completeLogData = {
                    userId: data.userId,
                    loginType: data.loginType,
                    loginStatus: user_login_log_entity_1.LoginStatus.SUCCESS,
                    clientIp: data.clientIp,
                    userAgent: data.userAgent,
                    deviceInfo: data.deviceInfo,
                    sessionId: data.sessionId,
                    location: locationInfo ? locationInfo.displayName : undefined,
                    country: locationInfo?.country || '中国',
                    province: locationInfo?.province,
                    city: locationInfo?.city,
                    isp: locationInfo?.isp,
                    locationSource: locationInfo?.dataSource || 'ip2region',
                    dataQuality: locationInfo?.confidence || 100,
                    riskLevel: riskAssessment?.level || user_login_log_entity_1.RiskLevel.LOW,
                    riskReason: riskAssessment?.reason
                };
                await LoginLoggerUtil.userLoginLogService.recordLogin(completeLogData);
                console.log('✅ 登录成功日志已保存到数据库', {
                    location: locationInfo?.displayName || '未知',
                    riskLevel: riskAssessment?.level || 'LOW',
                    responseTime: Date.now() - startTime
                });
            }
            else {
                console.warn('⚠️ UserLoginLogService 服务实例未初始化，无法保存到数据库');
            }
        }
        catch (error) {
            console.error('❌ 保存登录成功日志到数据库失败:', error);
            console.error('❌ 错误详情:', error.stack);
        }
    }
    static async logFailedLogin(data) {
        const logData = {
            timestamp: new Date().toISOString(),
            level: 'WARN',
            event: 'LOGIN_FAILED',
            ...data,
            userAgent: data.userAgent ? `${data.userAgent.substring(0, 100)}...` : '未知设备'
        };
        console.log('❌ [LOGIN_FAILED]', JSON.stringify(logData, null, 2));
        let locationInfo = null;
        if (data.enableLocationResolution !== false && data.clientIp && LoginLoggerUtil.ipLocationUtil) {
            try {
                console.log('🌍 解析失败登录IP地理位置:', LoginLoggerUtil.maskIP(data.clientIp));
                locationInfo = await LoginLoggerUtil.ipLocationUtil.getLocationByIP(data.clientIp);
                if (locationInfo.country !== '中国' || data.failReason?.includes('密码错误')) {
                    console.log('🚨 检测到可疑的失败登录:', {
                        ip: LoginLoggerUtil.maskIP(data.clientIp),
                        location: locationInfo.displayName,
                        failReason: data.failReason
                    });
                }
            }
            catch (locationError) {
                console.warn('⚠️ 失败登录IP地理位置解析失败:', locationError.message);
                locationInfo = null;
            }
        }
        try {
            if (LoginLoggerUtil.userLoginLogService && data.userId) {
                const completeLogData = {
                    userId: data.userId,
                    loginType: data.loginType,
                    loginStatus: user_login_log_entity_1.LoginStatus.FAILED,
                    clientIp: data.clientIp,
                    userAgent: data.userAgent,
                    failReason: data.failReason,
                    location: locationInfo ? locationInfo.displayName : undefined,
                    country: locationInfo?.country || '中国',
                    province: locationInfo?.province,
                    city: locationInfo?.city,
                    isp: locationInfo?.isp,
                    locationSource: locationInfo?.dataSource || 'ip2region',
                    dataQuality: locationInfo?.confidence || 100,
                    riskLevel: user_login_log_entity_1.RiskLevel.LOW,
                    riskReason: '登录失败'
                };
                await LoginLoggerUtil.userLoginLogService.recordLogin(completeLogData);
                console.log('✅ 登录失败日志已保存到数据库');
            }
        }
        catch (error) {
            console.error('❌ 保存登录失败日志到数据库失败:', error);
        }
    }
    static async logLogout(data) {
        const logData = {
            timestamp: new Date().toISOString(),
            level: 'INFO',
            event: 'LOGOUT',
            ...data
        };
        console.log('🚪 [LOGOUT]', JSON.stringify(logData, null, 2));
        try {
            if (LoginLoggerUtil.userLoginLogService) {
                await LoginLoggerUtil.userLoginLogService.recordLogout(data.userId, data.sessionId, data.reason, data.logoutType);
            }
        }
        catch (error) {
            console.error('❌ 保存登出日志到数据库失败:', error);
        }
    }
    static logTokenRefresh(data) {
        const logData = {
            timestamp: new Date().toISOString(),
            level: 'INFO',
            event: 'TOKEN_REFRESH',
            ...data,
            userAgent: data.userAgent ? `${data.userAgent.substring(0, 100)}...` : '未知设备'
        };
        console.log('🔄 [TOKEN_REFRESH]', JSON.stringify(logData, null, 2));
    }
    static logAbnormalLogin(data) {
        const logData = {
            timestamp: new Date().toISOString(),
            level: 'WARN',
            event: 'ABNORMAL_LOGIN',
            ...data,
            userAgent: data.userAgent ? `${data.userAgent.substring(0, 100)}...` : '未知设备'
        };
        console.log('⚠️ [ABNORMAL_LOGIN]', JSON.stringify(logData, null, 2));
    }
    static logSecurityEvent(data) {
        const logData = {
            timestamp: new Date().toISOString(),
            level: 'ERROR',
            event: 'SECURITY_EVENT',
            eventType: data.eventType,
            userId: data.userId,
            clientIp: data.clientIp,
            description: data.description,
            severity: data.severity
        };
        console.log('🚨 [SECURITY_EVENT]', JSON.stringify(logData, null, 2));
    }
    static getDeviceInfo(userAgent) {
        if (!userAgent)
            return '未知设备';
        let device = '未知设备';
        let browser = '未知浏览器';
        let os = '未知系统';
        if (userAgent.includes('Windows'))
            os = 'Windows';
        else if (userAgent.includes('Mac OS'))
            os = 'macOS';
        else if (userAgent.includes('Linux'))
            os = 'Linux';
        else if (userAgent.includes('Android'))
            os = 'Android';
        else if (userAgent.includes('iOS'))
            os = 'iOS';
        if (userAgent.includes('Chrome'))
            browser = 'Chrome';
        else if (userAgent.includes('Firefox'))
            browser = 'Firefox';
        else if (userAgent.includes('Safari'))
            browser = 'Safari';
        else if (userAgent.includes('Edge'))
            browser = 'Edge';
        if (userAgent.includes('Mobile'))
            device = '移动设备';
        else if (userAgent.includes('Tablet'))
            device = '平板设备';
        else
            device = '桌面设备';
        return `${device} - ${os} - ${browser}`;
    }
    static isSuspiciousIp(ip) {
        if (ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
            return false;
        }
        if (ip === '127.0.0.1' || ip === 'localhost') {
            return false;
        }
        return false;
    }
    static maskIP(ip) {
        if (!ip)
            return '未知IP';
        if (ip.includes(':')) {
            const parts = ip.split(':');
            if (parts.length >= 4) {
                return `${parts.slice(0, 4).join(':')}:****`;
            }
            return '****:****:****:****';
        }
        else {
            const parts = ip.split('.');
            if (parts.length === 4) {
                return `${parts[0]}.${parts[1]}.${parts[2]}.***`;
            }
            return '***.***.***.**';
        }
    }
}
exports.LoginLoggerUtil = LoginLoggerUtil;
//# sourceMappingURL=login-logger.util.js.map