import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, IsDateString } from 'class-validator';
import { LoginType, LoginStatus, RiskLevel } from '../entities/user_login_log.entity';

export class QueryUserLoginLogDto {
  @ApiProperty({ description: '用户ID', required: false })
  @IsOptional()
  @IsNumber({}, { message: '用户ID必须是数字' })
  userId?: number;

  @ApiProperty({ description: '登录方式', enum: LoginType, required: false })
  @IsOptional()
  @IsEnum(LoginType, { message: '登录方式必须是有效值' })
  loginType?: LoginType;

  @ApiProperty({ description: '登录状态', enum: LoginStatus, required: false })
  @IsOptional()
  @IsEnum(LoginStatus, { message: '登录状态必须是有效值' })
  loginStatus?: LoginStatus;

  @ApiProperty({ description: '客户端IP地址', required: false })
  @IsOptional()
  @IsString({ message: '客户端IP地址必须是字符串' })
  clientIp?: string;

  @ApiProperty({ description: '国家', required: false })
  @IsOptional()
  @IsString({ message: '国家必须是字符串' })
  country?: string;

  @ApiProperty({ description: '省份', required: false })
  @IsOptional()
  @IsString({ message: '省份必须是字符串' })
  province?: string;

  @ApiProperty({ description: '城市', required: false })
  @IsOptional()
  @IsString({ message: '城市必须是字符串' })
  city?: string;

  @ApiProperty({ description: '网络运营商', required: false })
  @IsOptional()
  @IsString({ message: '网络运营商必须是字符串' })
  isp?: string;

  @ApiProperty({
    description: '风险等级',
    enum: RiskLevel,
    required: false
  })
  @IsOptional()
  @IsEnum(RiskLevel, { message: '风险等级必须是有效值' })
  riskLevel?: RiskLevel;

  @ApiProperty({ description: '位置数据来源', required: false })
  @IsOptional()
  @IsString({ message: '位置数据来源必须是字符串' })
  locationSource?: string;

  @ApiProperty({ description: '开始时间', required: false })
  @IsOptional()
  @IsDateString({}, { message: '开始时间必须是有效的日期格式' })
  startTime?: string;

  @ApiProperty({ description: '结束时间', required: false })
  @IsOptional()
  @IsDateString({}, { message: '结束时间必须是有效的日期格式' })
  endTime?: string;

  @ApiProperty({ description: '页码', default: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  page?: number = 1;

  @ApiProperty({ description: '每页数量', default: 10, required: false })
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  limit?: number = 10;
}
