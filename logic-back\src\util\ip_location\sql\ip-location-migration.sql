-- IP地理位置功能数据库迁移脚本
-- 执行前请备份数据库
-- 执行时间：2024-01-15

-- 1. 扩展现有user_login_log表，添加详细地理位置字段
-- 注意：复用现有clientIp字段，现有location字段将被详细字段替代

-- 首先检查表结构
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
-- FROM INFORMATION_SCHEMA.COLUMNS
-- WHERE TABLE_NAME = 'user_login_log' AND TABLE_SCHEMA = DATABASE();

-- 添加详细地理位置字段
ALTER TABLE user_login_log
ADD COLUMN country VARCHAR(50) DEFAULT '中国' COMMENT '国家/地区',
ADD COLUMN province VARCHAR(50) COMMENT '省份/州',
ADD COLUMN city VARCHAR(50) COMMENT '城市',
ADD COLUMN district VARCHAR(50) COMMENT '区县',
ADD COLUMN isp VARCHAR(100) COMMENT '网络运营商/ISP',
ADD COLUMN latitude DECIMAL(10,7) COMMENT '纬度坐标',
ADD COLUMN longitude DECIMAL(10,7) COMMENT '经度坐标',
ADD COLUMN timezone VARCHAR(50) COMMENT '时区',
ADD COLUMN risk_level ENUM('LOW','MEDIUM','HIGH') DEFAULT 'LOW' COMMENT '风险等级：LOW-低风险,MEDIUM-中风险,HIGH-高风险',
ADD COLUMN risk_reason VARCHAR(200) COMMENT '风险原因描述',
ADD COLUMN location_source VARCHAR(30) DEFAULT 'ip2region' COMMENT '位置数据来源：ip2region,geoip,manual',
ADD COLUMN data_quality TINYINT UNSIGNED DEFAULT 100 COMMENT '数据质量评分(0-100)',
ADD COLUMN location_accuracy ENUM('COUNTRY','PROVINCE','CITY','DISTRICT','PRECISE') DEFAULT 'CITY' COMMENT '位置精度等级',
ADD COLUMN is_vpn BOOLEAN DEFAULT FALSE COMMENT '是否VPN/代理IP',
ADD COLUMN is_mobile BOOLEAN DEFAULT FALSE COMMENT '是否移动网络',
ADD COLUMN location_updated_at TIMESTAMP NULL COMMENT '位置信息更新时间';

-- 添加索引优化查询性能
ALTER TABLE user_login_log
ADD INDEX idx_user_location (userId, province, city),
ADD INDEX idx_location_risk (risk_level, location_accuracy),
ADD INDEX idx_location_source (location_source),
ADD INDEX idx_country_province (country, province),
ADD INDEX idx_coordinates (latitude, longitude),
ADD INDEX idx_isp (isp),
ADD INDEX idx_location_updated (location_updated_at);

-- 2. 创建用户常用登录地统计表
CREATE TABLE user_common_locations (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  userId BIGINT NOT NULL COMMENT '用户ID',
  country VARCHAR(20) DEFAULT '中国' COMMENT '国家',
  province VARCHAR(30) NOT NULL COMMENT '省份',
  city VARCHAR(30) NOT NULL COMMENT '城市',
  isp VARCHAR(50) COMMENT '主要运营商',
  loginCount INT DEFAULT 1 COMMENT '登录次数',
  firstLoginAt TIMESTAMP COMMENT '首次登录时间',
  lastLoginAt TIMESTAMP COMMENT '最后登录时间',
  isTrusted BOOLEAN DEFAULT FALSE COMMENT '是否为可信地区',
  trustScore DECIMAL(5,2) DEFAULT 0.00 COMMENT '信任评分(0-100)',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  -- 唯一键约束：每个用户的每个位置只能有一条记录
  UNIQUE KEY uk_user_location (userId, province, city),
  
  -- 索引优化
  INDEX idx_user_trusted (userId, isTrusted),
  INDEX idx_login_count (loginCount DESC),
  INDEX idx_trust_score (trustScore DESC),
  INDEX idx_user_id (userId),
  INDEX idx_last_login (lastLoginAt DESC)
) COMMENT='用户常用登录地统计表';

-- 3. 创建IP地理位置缓存表（可选，主要使用Redis缓存）
CREATE TABLE ip_location_cache (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  ipAddress VARCHAR(45) NOT NULL COMMENT 'IP地址',
  country VARCHAR(20) COMMENT '国家',
  province VARCHAR(30) COMMENT '省份',
  city VARCHAR(30) COMMENT '城市',
  isp VARCHAR(50) COMMENT '网络运营商',
  dataSource VARCHAR(20) DEFAULT 'ip2region' COMMENT '数据来源',
  confidence TINYINT DEFAULT 100 COMMENT '数据置信度',
  queryCount INT DEFAULT 1 COMMENT '查询次数',
  lastQueryAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后查询时间',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  -- 唯一键约束
  UNIQUE KEY uk_ip_address (ipAddress),
  
  -- 索引优化
  INDEX idx_query_count (queryCount DESC),
  INDEX idx_last_query (lastQueryAt DESC),
  INDEX idx_data_source (dataSource)
) COMMENT='IP地理位置缓存表（备用存储）';

-- 4. 插入一些测试数据（可选）
-- 注意：这些是示例数据，生产环境中请根据实际情况调整

-- 插入一些常见IP的位置信息
INSERT INTO ip_location_cache (ipAddress, country, province, city, isp, confidence) VALUES
('*******', '美国', '加利福尼亚州', '山景城', 'Google', 95),
('***************', '中国', '江苏省', '南京市', '电信', 98),
('*********', '中国', '浙江省', '杭州市', '阿里云', 98),
('*******', '美国', '加利福尼亚州', '旧金山', 'Cloudflare', 95);

-- 5. 创建存储过程：清理过期缓存数据
DELIMITER //

CREATE PROCEDURE CleanExpiredIpLocationCache()
BEGIN
    -- 清理90天前的缓存数据（查询次数少于10次的）
    DELETE FROM ip_location_cache 
    WHERE lastQueryAt < DATE_SUB(NOW(), INTERVAL 90 DAY) 
    AND queryCount < 10;
    
    -- 记录清理结果
    SELECT ROW_COUNT() as cleaned_records, NOW() as cleaned_at;
END //

DELIMITER ;

-- 6. 创建视图：用户位置统计视图
CREATE VIEW user_location_stats_view AS
SELECT 
    ucl.userId,
    COUNT(*) as totalLocations,
    SUM(CASE WHEN ucl.isTrusted = 1 THEN 1 ELSE 0 END) as trustedLocations,
    SUM(ucl.loginCount) as totalLoginCount,
    AVG(ucl.trustScore) as avgTrustScore,
    MAX(ucl.lastLoginAt) as lastLoginAt,
    MIN(ucl.firstLoginAt) as firstLoginAt
FROM user_common_locations ucl
GROUP BY ucl.userId;

-- 7. 创建触发器：自动更新用户常用位置的信任评分
DELIMITER //

CREATE TRIGGER update_trust_score_after_login
AFTER UPDATE ON user_common_locations
FOR EACH ROW
BEGIN
    -- 当登录次数更新时，自动重新计算信任评分
    IF NEW.loginCount != OLD.loginCount THEN
        UPDATE user_common_locations 
        SET trustScore = LEAST(
            -- 基础评分：基于登录次数
            LEAST(NEW.loginCount * 2, 60) +
            -- 时间奖励：使用时长
            CASE 
                WHEN NEW.firstLoginAt IS NOT NULL THEN
                    LEAST(DATEDIFF(NOW(), NEW.firstLoginAt) / 30 * 20, 20)
                ELSE 0 
            END +
            -- 活跃度奖励
            CASE 
                WHEN NEW.lastLoginAt IS NOT NULL AND DATEDIFF(NOW(), NEW.lastLoginAt) <= 7 THEN 10
                WHEN NEW.lastLoginAt IS NOT NULL AND DATEDIFF(NOW(), NEW.lastLoginAt) > 90 THEN -10
                ELSE 0 
            END +
            -- 可信位置奖励
            CASE WHEN NEW.isTrusted = 1 THEN 20 ELSE 0 END,
            100
        )
        WHERE id = NEW.id;
    END IF;
END //

DELIMITER ;

-- 8. 权限设置（根据实际需要调整）
-- GRANT SELECT, INSERT, UPDATE ON user_common_locations TO 'app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE ON ip_location_cache TO 'app_user'@'%';
-- GRANT SELECT ON user_location_stats_view TO 'app_user'@'%';

-- 迁移完成提示
SELECT 'IP地理位置功能数据库迁移完成' as message, NOW() as completed_at;
