"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryUserLoginLogDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const user_login_log_entity_1 = require("../entities/user_login_log.entity");
class QueryUserLoginLogDto {
    userId;
    loginType;
    loginStatus;
    clientIp;
    country;
    province;
    city;
    isp;
    riskLevel;
    locationSource;
    startTime;
    endTime;
    page = 1;
    limit = 10;
}
exports.QueryUserLoginLogDto = QueryUserLoginLogDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '用户ID必须是数字' }),
    __metadata("design:type", Number)
], QueryUserLoginLogDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '登录方式', enum: user_login_log_entity_1.LoginType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(user_login_log_entity_1.LoginType, { message: '登录方式必须是有效值' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "loginType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '登录状态', enum: user_login_log_entity_1.LoginStatus, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(user_login_log_entity_1.LoginStatus, { message: '登录状态必须是有效值' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "loginStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '客户端IP地址', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '客户端IP地址必须是字符串' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "clientIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '国家', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '国家必须是字符串' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '省份', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '省份必须是字符串' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '城市', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '城市必须是字符串' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '网络运营商', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '网络运营商必须是字符串' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "isp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '风险等级',
        enum: user_login_log_entity_1.RiskLevel,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(user_login_log_entity_1.RiskLevel, { message: '风险等级必须是有效值' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "riskLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '位置数据来源', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '位置数据来源必须是字符串' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "locationSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '开始时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '开始时间必须是有效的日期格式' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '结束时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '结束时间必须是有效的日期格式' }),
    __metadata("design:type", String)
], QueryUserLoginLogDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', default: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '页码必须是数字' }),
    __metadata("design:type", Number)
], QueryUserLoginLogDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', default: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '每页数量必须是数字' }),
    __metadata("design:type", Number)
], QueryUserLoginLogDto.prototype, "limit", void 0);
//# sourceMappingURL=query-user-login-log.dto.js.map