"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserLoginLogDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const user_login_log_entity_1 = require("../entities/user_login_log.entity");
class CreateUserLoginLogDto {
    userId;
    loginType;
    loginStatus;
    clientIp;
    userAgent;
    deviceInfo;
    location;
    country;
    province;
    city;
    isp;
    riskLevel;
    riskReason;
    locationSource;
    dataQuality;
    sessionId;
    tokenExpireTime;
    failReason;
}
exports.CreateUserLoginLogDto = CreateUserLoginLogDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '用户ID必须是数字' }),
    __metadata("design:type", Number)
], CreateUserLoginLogDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '登录方式', enum: user_login_log_entity_1.LoginType }),
    (0, class_validator_1.IsNotEmpty)({ message: '登录方式不能为空' }),
    (0, class_validator_1.IsEnum)(user_login_log_entity_1.LoginType, { message: '登录方式必须是有效值' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "loginType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '登录状态', enum: user_login_log_entity_1.LoginStatus }),
    (0, class_validator_1.IsNotEmpty)({ message: '登录状态不能为空' }),
    (0, class_validator_1.IsEnum)(user_login_log_entity_1.LoginStatus, { message: '登录状态必须是有效值' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "loginStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '客户端IP地址' }),
    (0, class_validator_1.IsNotEmpty)({ message: '客户端IP地址不能为空' }),
    (0, class_validator_1.IsString)({ message: '客户端IP地址必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "clientIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户代理信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '用户代理信息必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "userAgent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设备信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '设备信息必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "deviceInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'IP地址对应的地理位置（原始字段）', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '地理位置必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '国家', default: '中国' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '国家必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '省份', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '省份必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '城市', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '城市必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '网络运营商', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '网络运营商必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "isp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '风险等级',
        enum: user_login_log_entity_1.RiskLevel,
        default: user_login_log_entity_1.RiskLevel.LOW,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(user_login_log_entity_1.RiskLevel, { message: '风险等级必须是有效值' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "riskLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '风险原因', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '风险原因必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "riskReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '位置数据来源', default: 'ip2region', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '位置数据来源必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "locationSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '数据质量评分(0-100)', default: 100, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '数据质量评分必须是数字' }),
    __metadata("design:type", Number)
], CreateUserLoginLogDto.prototype, "dataQuality", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '会话ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '会话ID必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Token过期时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Token过期时间必须是有效的日期格式' }),
    __metadata("design:type", Date)
], CreateUserLoginLogDto.prototype, "tokenExpireTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '登录失败原因', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '登录失败原因必须是字符串' }),
    __metadata("design:type", String)
], CreateUserLoginLogDto.prototype, "failReason", void 0);
//# sourceMappingURL=create-user-login-log.dto.js.map