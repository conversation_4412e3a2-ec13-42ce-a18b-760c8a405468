import { UserLoginLogService } from '../../util/database/mysql/user_login_log/user_login_log.service';
import { LoginType } from '../../util/database/mysql/user_login_log/entities/user_login_log.entity';
import { IpLocationUtil } from '../../util/ip_location/utils/ip-location.util';
import { RiskAssessmentUtil } from '../../util/ip_location/utils/risk-assessment.util';
export declare class LoginLoggerUtil {
    private static userLoginLogService;
    private static ipLocationUtil;
    private static riskAssessmentUtil;
    static setService(service: UserLoginLogService): void;
    static setIpLocationService(ipLocationUtil: IpLocationUtil, riskAssessmentUtil: RiskAssessmentUtil): void;
    static logSuccessLogin(data: {
        userId: number;
        loginType: LoginType;
        clientIp: string;
        userAgent?: string;
        deviceInfo?: string;
        sessionId?: string;
        enableLocationResolution?: boolean;
    }): Promise<void>;
    static logFailedLogin(data: {
        userId?: number;
        phone?: string;
        loginType: LoginType;
        clientIp: string;
        userAgent?: string;
        failReason: string;
        enableLocationResolution?: boolean;
    }): Promise<void>;
    static logLogout(data: {
        userId: number;
        clientIp: string;
        sessionId?: string;
        reason?: string;
        logoutType?: 'active' | 'passive' | 'switch';
    }): Promise<void>;
    static logTokenRefresh(data: {
        userId: number;
        clientIp: string;
        userAgent?: string;
        oldTokenExpired?: boolean;
    }): void;
    static logAbnormalLogin(data: {
        userId: number;
        clientIp: string;
        userAgent?: string;
        reason: string;
        riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    }): void;
    static logSecurityEvent(data: {
        userId?: number;
        clientIp: string;
        eventType: 'BRUTE_FORCE' | 'SUSPICIOUS_IP' | 'MULTIPLE_DEVICE' | 'TOKEN_HIJACK';
        description: string;
        severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    }): void;
    static getDeviceInfo(userAgent: string): string;
    static isSuspiciousIp(ip: string): boolean;
    static maskIP(ip: string): string;
}
