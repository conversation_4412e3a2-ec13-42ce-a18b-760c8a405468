import { Repository } from 'typeorm';
import { UserLoginLog, LoginType, LoginStatus, RiskLevel } from './entities/user_login_log.entity';
import { CreateUserLoginLogDto } from './dto/create-user-login-log.dto';
import { QueryUserLoginLogDto } from './dto/query-user-login-log.dto';
export interface LoginLogData {
    userId: number;
    loginType: LoginType;
    loginStatus: LoginStatus;
    clientIp: string;
    userAgent?: string;
    deviceInfo?: string;
    location?: string;
    sessionId?: string;
    tokenExpireTime?: Date;
    failReason?: string;
    country?: string;
    province?: string;
    city?: string;
    isp?: string;
    riskLevel?: RiskLevel;
    riskReason?: string;
    locationSource?: string;
    dataQuality?: number;
}
export declare class UserLoginLogService {
    private readonly userLoginLogRepository;
    constructor(userLoginLogRepository: Repository<UserLoginLog>);
    recordLogin(data: LoginLogData): Promise<UserLoginLog>;
    recordLogout(userId: number, sessionId?: string, reason?: string, logoutType?: 'active' | 'passive' | 'switch'): Promise<void>;
    getUserLoginHistory(userId: number, limit?: number): Promise<UserLoginLog[]>;
    getLastLogin(userId: number): Promise<UserLoginLog | null>;
    findWithPagination(query: QueryUserLoginLogDto): Promise<{
        data: UserLoginLog[];
        total: number;
        page: number;
        limit: number;
    }>;
    checkAbnormalLogin(userId: number, clientIp: string, userAgent: string): Promise<boolean>;
    private extractBrowserInfo;
    getLoginStats(userId: number, days?: number): Promise<any>;
    create(createDto: CreateUserLoginLogDto): Promise<UserLoginLog>;
    findOne(id: number): Promise<UserLoginLog | null>;
    remove(id: number): Promise<void>;
}
