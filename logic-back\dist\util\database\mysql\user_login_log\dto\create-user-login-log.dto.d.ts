import { LoginType, LoginStatus, RiskLevel } from '../entities/user_login_log.entity';
export declare class CreateUserLoginLogDto {
    userId: number;
    loginType: LoginType;
    loginStatus: LoginStatus;
    clientIp: string;
    userAgent?: string;
    deviceInfo?: string;
    location?: string;
    country?: string;
    province?: string;
    city?: string;
    isp?: string;
    riskLevel?: RiskLevel;
    riskReason?: string;
    locationSource?: string;
    dataQuality?: number;
    sessionId?: string;
    tokenExpireTime?: Date;
    failReason?: string;
}
