"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginLoggerInitializerService = void 0;
const common_1 = require("@nestjs/common");
const user_login_log_service_1 = require("../../util/database/mysql/user_login_log/user_login_log.service");
const login_logger_util_1 = require("./login-logger.util");
const ip_location_util_1 = require("../../util/ip_location/utils/ip-location.util");
const risk_assessment_util_1 = require("../../util/ip_location/utils/risk-assessment.util");
let LoginLoggerInitializerService = class LoginLoggerInitializerService {
    userLoginLogService;
    ipLocationUtil;
    riskAssessmentUtil;
    constructor(userLoginLogService, ipLocationUtil, riskAssessmentUtil) {
        this.userLoginLogService = userLoginLogService;
        this.ipLocationUtil = ipLocationUtil;
        this.riskAssessmentUtil = riskAssessmentUtil;
    }
    onModuleInit() {
        console.log('🚀 开始初始化 LoginLoggerUtil...');
        console.log('🔍 服务实例信息:', {
            hasUserLoginLogService: !!this.userLoginLogService,
            hasIpLocationUtil: !!this.ipLocationUtil,
            hasRiskAssessmentUtil: !!this.riskAssessmentUtil,
            userLoginLogServiceType: this.userLoginLogService?.constructor?.name
        });
        login_logger_util_1.LoginLoggerUtil.setService(this.userLoginLogService);
        if (this.ipLocationUtil && this.riskAssessmentUtil) {
            login_logger_util_1.LoginLoggerUtil.setIpLocationService(this.ipLocationUtil, this.riskAssessmentUtil);
            console.log('✅ IP地理位置服务已注入到 LoginLoggerUtil');
        }
        else {
            console.log('⚠️ IP地理位置服务不可用，LoginLoggerUtil 将使用基础功能');
        }
        console.log('✅ LoginLoggerUtil 初始化完成');
    }
};
exports.LoginLoggerInitializerService = LoginLoggerInitializerService;
exports.LoginLoggerInitializerService = LoginLoggerInitializerService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Optional)()),
    __param(2, (0, common_1.Optional)()),
    __metadata("design:paramtypes", [user_login_log_service_1.UserLoginLogService,
        ip_location_util_1.IpLocationUtil,
        risk_assessment_util_1.RiskAssessmentUtil])
], LoginLoggerInitializerService);
//# sourceMappingURL=login-logger-initializer.service.js.map