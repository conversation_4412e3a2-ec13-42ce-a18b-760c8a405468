import { Column, Entity, Index, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

// 登录方式枚举
export enum LoginType {
  PASSWORD = 'password',  // 密码登录
  SMS = 'sms',           // 短信登录
  QRCODE = 'qrcode',     // 扫码登录
  REFRESH = 'refresh'    // Token刷新
}

// 登录状态枚举
export enum LoginStatus {
  SUCCESS = 'success',   // 登录成功
  FAILED = 'failed',     // 登录失败
  LOGOUT = 'logout'      // 登出
}

// 登出类型枚举
export enum LogoutType {
  ACTIVE = 'active',    // 主动登出：用户点击登出按钮
  PASSIVE = 'passive',  // 被动登出：被挤出、Token过期、管理员强制等
  SWITCH = 'switch'     // 切换登出：角色切换、身份切换时的登出
}

// 风险等级枚举
export enum RiskLevel {
  LOW = 'LOW',          // 低风险：正常登录
  MEDIUM = 'MEDIUM',    // 中风险：异地登录、新设备等
  HIGH = 'HIGH'         // 高风险：异常IP、可疑行为等
}

/**
 * 用户登录日志
 */
@Entity('user_login_log')
@Index(['userId'])
@Index(['loginTime'])
@Index(['clientIp'])
@Index(['loginStatus'])
@Index(['loginType'])
@Index('idx_user_location', ['userId', 'province', 'city'])
@Index('idx_risk_level', ['riskLevel'])
@Index('idx_location_source', ['locationSource'])
export class UserLoginLog {
  @PrimaryGeneratedColumn({ type: 'bigint', comment: '主键ID' })
  @ApiProperty({ description: '主键ID' })
  id: number;

  @Column({ type: 'bigint', comment: '用户ID' })
  @ApiProperty({ description: '用户ID' })
  userId: number;

  @Column({ 
    type: 'varchar', 
    length: 20, 
    comment: '登录方式：password-密码登录, sms-短信登录, qrcode-扫码登录, refresh-token刷新' 
  })
  @ApiProperty({ description: '登录方式', enum: ['password', 'sms', 'qrcode', 'refresh'] })
  loginType: string;

  @Column({ 
    type: 'varchar', 
    length: 20, 
    comment: '登录状态：success-成功, failed-失败, logout-登出' 
  })
  @ApiProperty({ description: '登录状态', enum: ['success', 'failed', 'logout'] })
  loginStatus: string;

  @Column({ 
    type: 'varchar', 
    length: 45, 
    comment: '客户端IP地址（支持IPv6）' 
  })
  @ApiProperty({ description: '客户端IP地址' })
  clientIp: string;

  @Column({ 
    type: 'text', 
    nullable: true, 
    comment: '用户代理信息（浏览器、设备等）' 
  })
  @ApiProperty({ description: '用户代理信息', required: false })
  userAgent?: string;

  @Column({ 
    type: 'varchar', 
    length: 500, 
    nullable: true, 
    comment: '设备信息（操作系统、浏览器版本等）' 
  })
  @ApiProperty({ description: '设备信息', required: false })
  deviceInfo?: string;

  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: 'IP地址对应的地理位置（原始字段，保留兼容性）'
  })
  @ApiProperty({ description: 'IP地址对应的地理位置', required: false })
  location?: string;

  @Column({
    type: 'varchar',
    length: 20,
    default: '中国',
    comment: '国家'
  })
  @ApiProperty({ description: '国家', default: '中国' })
  country: string;

  @Column({
    type: 'varchar',
    length: 30,
    nullable: true,
    comment: '省份'
  })
  @ApiProperty({ description: '省份', required: false })
  province?: string;

  @Column({
    type: 'varchar',
    length: 30,
    nullable: true,
    comment: '城市'
  })
  @ApiProperty({ description: '城市', required: false })
  city?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '网络运营商'
  })
  @ApiProperty({ description: '网络运营商', required: false })
  isp?: string;

  @Column({
    type: 'enum',
    enum: RiskLevel,
    default: RiskLevel.LOW,
    comment: '风险等级'
  })
  @ApiProperty({
    description: '风险等级',
    enum: RiskLevel,
    default: RiskLevel.LOW
  })
  riskLevel: RiskLevel;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '风险原因'
  })
  @ApiProperty({ description: '风险原因', required: false })
  riskReason?: string;

  @Column({
    type: 'varchar',
    length: 20,
    default: 'ip2region',
    comment: '位置数据来源'
  })
  @ApiProperty({ description: '位置数据来源', default: 'ip2region' })
  locationSource: string;

  @Column({
    type: 'tinyint',
    default: 100,
    comment: '数据质量评分(0-100)'
  })
  @ApiProperty({ description: '数据质量评分(0-100)', default: 100 })
  dataQuality: number;

  @Column({ 
    type: 'varchar', 
    length: 100, 
    nullable: true, 
    comment: '会话ID' 
  })
  @ApiProperty({ description: '会话ID', required: false })
  sessionId?: string;

  @Column({ 
    type: 'datetime',  
    nullable: true, 
    comment: 'Token过期时间' 
  })
  @ApiProperty({ description: 'Token过期时间', required: false })
  tokenExpireTime?: Date;

  @Column({ 
    type: 'varchar', 
    length: 200, 
    nullable: true, 
    comment: '登录失败原因' 
  })
  @ApiProperty({ description: '登录失败原因', required: false })
  failReason?: string;

  @Column({ 
    type: 'datetime', 
    default: () => 'CURRENT_TIMESTAMP', 
    comment: '登录时间' 
  })
  @ApiProperty({ description: '登录时间' })
  loginTime: Date;

  @Column({
    type: 'datetime',
    nullable: true,
    comment: '登出时间'
  })
  @ApiProperty({ description: '登出时间', required: false })
  logoutTime?: Date;

  @Column({
    type: 'enum',
    enum: ['active', 'passive', 'switch'],
    nullable: true,
    comment: '登出类型：active-主动登出, passive-被动登出, switch-切换登出'
  })
  @ApiProperty({
    description: '登出类型',
    enum: ['active', 'passive', 'switch'],
    required: false
  })
  logoutType?: 'active' | 'passive' | 'switch';

  @Column({ 
    type: 'int', 
    nullable: true, 
    comment: '会话持续时间（秒）' 
  })
  @ApiProperty({ description: '会话持续时间（秒）', required: false })
  duration?: number;

  @CreateDateColumn({ 
    type: 'datetime', 
    comment: '创建时间' 
  })
  @ApiProperty({ description: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ 
    type: 'datetime', 
    comment: '更新时间' 
  })
  @ApiProperty({ description: '更新时间' })
  updateTime: Date;
}




