import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsOptional, IsEnum, IsDateString } from 'class-validator';
import { LoginType, LoginStatus, RiskLevel } from '../entities/user_login_log.entity';

export class CreateUserLoginLogDto {
  @ApiProperty({ description: '用户ID' })
  @IsNotEmpty({ message: '用户ID不能为空' })
  @IsNumber({}, { message: '用户ID必须是数字' })
  userId: number;

  @ApiProperty({ description: '登录方式', enum: LoginType })
  @IsNotEmpty({ message: '登录方式不能为空' })
  @IsEnum(LoginType, { message: '登录方式必须是有效值' })
  loginType: LoginType;

  @ApiProperty({ description: '登录状态', enum: LoginStatus })
  @IsNotEmpty({ message: '登录状态不能为空' })
  @IsEnum(LoginStatus, { message: '登录状态必须是有效值' })
  loginStatus: LoginStatus;

  @ApiProperty({ description: '客户端IP地址' })
  @IsNotEmpty({ message: '客户端IP地址不能为空' })
  @IsString({ message: '客户端IP地址必须是字符串' })
  clientIp: string;

  @ApiProperty({ description: '用户代理信息', required: false })
  @IsOptional()
  @IsString({ message: '用户代理信息必须是字符串' })
  userAgent?: string;

  @ApiProperty({ description: '设备信息', required: false })
  @IsOptional()
  @IsString({ message: '设备信息必须是字符串' })
  deviceInfo?: string;

  @ApiProperty({ description: 'IP地址对应的地理位置（原始字段）', required: false })
  @IsOptional()
  @IsString({ message: '地理位置必须是字符串' })
  location?: string;

  @ApiProperty({ description: '国家', default: '中国' })
  @IsOptional()
  @IsString({ message: '国家必须是字符串' })
  country?: string;

  @ApiProperty({ description: '省份', required: false })
  @IsOptional()
  @IsString({ message: '省份必须是字符串' })
  province?: string;

  @ApiProperty({ description: '城市', required: false })
  @IsOptional()
  @IsString({ message: '城市必须是字符串' })
  city?: string;

  @ApiProperty({ description: '网络运营商', required: false })
  @IsOptional()
  @IsString({ message: '网络运营商必须是字符串' })
  isp?: string;

  @ApiProperty({
    description: '风险等级',
    enum: RiskLevel,
    default: RiskLevel.LOW,
    required: false
  })
  @IsOptional()
  @IsEnum(RiskLevel, { message: '风险等级必须是有效值' })
  riskLevel?: RiskLevel;

  @ApiProperty({ description: '风险原因', required: false })
  @IsOptional()
  @IsString({ message: '风险原因必须是字符串' })
  riskReason?: string;

  @ApiProperty({ description: '位置数据来源', default: 'ip2region', required: false })
  @IsOptional()
  @IsString({ message: '位置数据来源必须是字符串' })
  locationSource?: string;

  @ApiProperty({ description: '数据质量评分(0-100)', default: 100, required: false })
  @IsOptional()
  @IsNumber({}, { message: '数据质量评分必须是数字' })
  dataQuality?: number;

  @ApiProperty({ description: '会话ID', required: false })
  @IsOptional()
  @IsString({ message: '会话ID必须是字符串' })
  sessionId?: string;

  @ApiProperty({ description: 'Token过期时间', required: false })
  @IsOptional()
  @IsDateString({}, { message: 'Token过期时间必须是有效的日期格式' })
  tokenExpireTime?: Date;

  @ApiProperty({ description: '登录失败原因', required: false })
  @IsOptional()
  @IsString({ message: '登录失败原因必须是字符串' })
  failReason?: string;
}
