import { Injectable, OnModuleInit, Optional } from '@nestjs/common';
import { UserLoginLogService } from '../../util/database/mysql/user_login_log/user_login_log.service';
import { LoginLoggerUtil } from './login-logger.util';
import { IpLocationUtil } from '../../util/ip_location/utils/ip-location.util';
import { RiskAssessmentUtil } from '../../util/ip_location/utils/risk-assessment.util';

/**
 * 登录日志工具类初始化服务
 * 在模块初始化时将服务实例注入到工具类中，包括IP地理位置解析服务
 */
@Injectable()
export class LoginLoggerInitializerService implements OnModuleInit {
  constructor(
    private readonly userLoginLogService: UserLoginLogService,
    @Optional() private readonly ipLocationUtil?: IpLocationUtil,
    @Optional() private readonly riskAssessmentUtil?: RiskAssessmentUtil,
  ) {}

  onModuleInit() {
    console.log('🚀 开始初始化 LoginLoggerUtil...');
    console.log('🔍 服务实例信息:', {
      hasUserLoginLogService: !!this.userLoginLogService,
      hasIpLocationUtil: !!this.ipLocationUtil,
      hasRiskAssessmentUtil: !!this.riskAssessmentUtil,
      userLoginLogServiceType: this.userLoginLogService?.constructor?.name
    });

    // 将基础服务实例注入到工具类中
    LoginLoggerUtil.setService(this.userLoginLogService);

    // 如果IP地理位置服务可用，则注入
    if (this.ipLocationUtil && this.riskAssessmentUtil) {
      LoginLoggerUtil.setIpLocationService(this.ipLocationUtil, this.riskAssessmentUtil);
      console.log('✅ IP地理位置服务已注入到 LoginLoggerUtil');
    } else {
      console.log('⚠️ IP地理位置服务不可用，LoginLoggerUtil 将使用基础功能');
    }

    console.log('✅ LoginLoggerUtil 初始化完成');
  }
}
