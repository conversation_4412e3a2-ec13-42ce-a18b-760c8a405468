import { OnModuleInit } from '@nestjs/common';
import { UserLoginLogService } from '../../util/database/mysql/user_login_log/user_login_log.service';
import { IpLocationUtil } from '../../util/ip_location/utils/ip-location.util';
import { RiskAssessmentUtil } from '../../util/ip_location/utils/risk-assessment.util';
export declare class LoginLoggerInitializerService implements OnModuleInit {
    private readonly userLoginLogService;
    private readonly ipLocationUtil?;
    private readonly riskAssessmentUtil?;
    constructor(userLoginLogService: UserLoginLogService, ipLocationUtil?: IpLocationUtil | undefined, riskAssessmentUtil?: RiskAssessmentUtil | undefined);
    onModuleInit(): void;
}
