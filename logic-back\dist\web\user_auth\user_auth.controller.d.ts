import { HttpStatus } from '@nestjs/common';
import { UserAuthService } from './user_auth.service';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
import { UserInfoService } from 'src/util/database/mysql/user_info/user_info.service';
import { UserStudentService } from 'src/util/database/mysql/user_student/user_student.service';
import { UserSchoolService } from 'src/util/database/mysql/user_school/user_school.service';
import { WebWeixinScanService } from '../web_weixin_scan/web_weixin_scan.service';
import { RedisService } from 'src/util/database/redis/redis.service';
import { UserRoleRelationService } from 'src/util/database/mysql/user_role_relation/user_role_relation.service';
export declare class UserAuthController {
    private readonly userAuthService;
    private readonly userInfoService;
    private readonly userStudentService;
    private readonly userSchoolService;
    private readonly httpResponseResultService;
    private readonly webWeixinScanService;
    private readonly redisService;
    private readonly userRoleRelationService;
    private readonly logger;
    constructor(userAuthService: UserAuthService, userInfoService: UserInfoService, userStudentService: UserStudentService, userSchoolService: UserSchoolService, httpResponseResultService: HttpResponseResultService, webWeixinScanService: WebWeixinScanService, redisService: RedisService, userRoleRelationService: UserRoleRelationService);
    bindPhone(phone: string, code: string, replaceUserId: number, currentUser: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<string>>;
    setPasswordByPhone(phone: string, password: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<{
        code: number;
        message: string;
    }>>;
    setPasswordByUserId(userId: number, password: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<{
        code: number;
        message: string;
    }>>;
    password(phone: string, password: string, clientInfo: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        userInfo: {
            id: any;
            nickName: any;
            avatarUrl: any;
            phone: any;
            gender: any;
            roleId: any;
            registerType: any;
        };
        expire: any;
        token: string;
        refreshExpire: any;
        refreshToken: string;
    }> | import("../http_response_result/http-response.interface").HttpResponse<{
        roleList: {
            avatar: string;
            userId: number;
            roleId: number;
            roleName: string;
            nickName: string;
            schoolInfo: {
                province: string;
                city: string;
                district: string;
                schoolName: string;
            } | null;
            studentInfo: {
                studentNumber: string;
            } | null;
            token: string;
        }[];
        sessionId: any;
    }>>;
    selectIdentity(userId: number, sessionId: string, clientInfo: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        userInfo: {
            id: any;
            nickName: any;
            avatarUrl: any;
            phone: any;
            gender: any;
            roleId: any;
            registerType: any;
        };
        expire: any;
        token: string;
        refreshExpire: any;
        refreshToken: string;
    }>>;
    refreshToken(refreshToken: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<{
        expire: any;
        token: string;
        refreshExpire: any;
        refreshToken: string;
    }>>;
    updatePassword(request: any, oldPassword: string, newPassword: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<null>>;
    logout(clientInfo: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null>>;
    student(body: {
        province: string;
        city: string;
        district: string;
        schoolName: string;
        studentNumber: string;
        password: string;
    }, clientInfo: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        userInfo: {
            id: any;
            nickName: any;
            avatarUrl: any;
            phone: any;
            gender: any;
            roleId: any;
            registerType: any;
        };
        expire: any;
        token: string;
        refreshExpire: any;
        refreshToken: string;
    }>>;
    sendSmsCode(phone: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<{
        success: boolean;
        message: string;
        data: null;
    } | {
        success: boolean;
        message: string;
        data: boolean;
    }>>;
    sendSmsCodeUpdatePhone(phone: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        success: boolean;
        message: string;
        data: null;
    } | {
        success: boolean;
        message: string;
        data: boolean;
    }>>;
    smsLogin(params: {
        phone: string;
        code: string;
    }, clientInfo: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        isNewUser: boolean;
        needSetPwd: boolean;
        userInfo: {
            id: any;
            nickName: any;
            avatarUrl: any;
            phone: any;
            gender: any;
            roleId: any;
            registerType: any;
        };
        expire: any;
        token: string;
        refreshExpire: any;
        refreshToken: string;
    }> | import("../http_response_result/http-response.interface").HttpResponse<{
        roleList: {
            avatar: string;
            userId: number;
            roleId: number;
            roleName: string;
            nickName: string;
            schoolInfo: {
                province: string;
                city: string;
                district: string;
                schoolName: string;
            } | null;
            studentInfo: {
                studentNumber: string;
            } | null;
            token: string;
        }[];
        sessionId: any;
    }>>;
    resetPasswordByCode(params: {
        phone: string;
        code: string;
        newPassword: string;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null>>;
    resetStudentPassword(body: {
        studentIds: number[];
        password: string;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        successStudents: {
            id: number;
        }[];
        errorStudents: {
            id: number;
            reason: string;
        }[];
    }>>;
    checkPhoneAccounts(phone: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    findAllBindByPhone(phone: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<any[]> | import("../http_response_result/http-response.interface").HttpResponse<null>>;
    bindWeixinToUser(bindDto: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    registerAndBindWeixin(registerDto: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    bindWeixin(bindDto: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    resetPasswordByPhone(body: {
        oldPassword: string;
        newPassword: string;
        phone: string;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        success: number;
        total: number;
    }>>;
    switchRole(currentUser: any, roleId: number, clientInfo: any): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        userInfo: {
            id: any;
            nickName: any;
            avatarUrl: any;
            phone: any;
            gender: any;
            roleId: any;
            registerType: any;
        };
        expire: any;
        token: string;
        refreshExpire: any;
        refreshToken: string;
    }>>;
    verifySmsCode(phone: string, code: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<null>>;
    transferWeixinOpenid(request: any, targetUserId: number, verifyCode: string, bindPhone: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<any>>;
    autoLoginByUserId(request: any, userId: number, loginToken: string): Promise<import("../http_response_result/http-response.interface").HttpResponse<any> | {
        code: HttpStatus;
        message: string;
    }>;
    private getClientIp;
}
