2025-08-01 10:09:09.411 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-01 10:09:09.577 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.577 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.578 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.579 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.579 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.580 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.581 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.581 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.582 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.582 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.582 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.583 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.583 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.584 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.584 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.585 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.585 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.586 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.586 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:09.587 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:09:43.254 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/health - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-01 10:09:43.255 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":42844,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/health","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-08-01T02:09:43.255Z"}
2025-08-01 10:26:45.202 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-01 10:26:45.309 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.310 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.310 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.311 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.312 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.312 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.312 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.313 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.313 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.314 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.315 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.315 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.316 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.316 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.317 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.317 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.317 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.318 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.318 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:45.319 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95176,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.414 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-01 10:26:59.522 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.523 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.523 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.523 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.524 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.524 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.525 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.525 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.526 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.526 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.527 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.527 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.527 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.528 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.529 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.530 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.530 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.531 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.532 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:26:59.532 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":94856,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.778 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-01 10:27:35.857 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.858 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.858 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.859 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.859 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.860 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.860 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.860 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.860 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.861 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.861 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.861 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.862 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.862 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.862 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.863 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.863 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.863 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.864 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:35.864 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":93928,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.587 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-01 10:27:57.718 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.719 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.720 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.720 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.721 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.721 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.722 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.722 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.723 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.723 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.724 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.724 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.725 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.726 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.726 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.727 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.727 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.728 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.729 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:27:57.730 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91688,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.471 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-01 10:28:56.577 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.578 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.579 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.579 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.580 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.580 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.581 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.581 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.582 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.582 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.585 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.585 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.586 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.586 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.588 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.589 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.592 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.593 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.594 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:28:56.594 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":72180,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.643 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-01 10:30:17.743 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.744 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.744 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.745 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.745 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.746 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.747 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.747 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.747 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.748 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.748 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.749 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.749 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.749 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.750 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.750 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.751 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.751 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.752 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-01 10:30:17.752 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90360,"hostname":"DESKTOP-1L38AEG","stack":[null]}
