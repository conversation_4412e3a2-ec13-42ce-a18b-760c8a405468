{"version": 3, "file": "login-logger.util.js", "sourceRoot": "", "sources": ["../../../src/web/user_login_log/login-logger.util.ts"], "names": [], "mappings": ";;;AACA,mHAA4H;AAO5H,MAAa,eAAe;IAClB,MAAM,CAAC,mBAAmB,CAAsB;IAChD,MAAM,CAAC,cAAc,CAAiB;IACtC,MAAM,CAAC,kBAAkB,CAAqB;IAGtD,MAAM,CAAC,UAAU,CAAC,OAA4B;QAC5C,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE;YACzC,UAAU,EAAE,CAAC,CAAC,OAAO;YACrB,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI;SACxC,CAAC,CAAC;QACH,eAAe,CAAC,mBAAmB,GAAG,OAAO,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAGD,MAAM,CAAC,oBAAoB,CAAC,cAA8B,EAAE,kBAAsC;QAChG,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE;YAC/B,iBAAiB,EAAE,CAAC,CAAC,cAAc;YACnC,qBAAqB,EAAE,CAAC,CAAC,kBAAkB;SAC5C,CAAC,CAAC;QACH,eAAe,CAAC,cAAc,GAAG,cAAc,CAAC;QAChD,eAAe,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAClC,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,IAQ5B;QACC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,eAAe;YACtB,GAAG,IAAI;YACP,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM;SAC9E,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAGnE,IAAI,YAAY,GAAmC,IAAI,CAAC;QACxD,IAAI,cAAc,GAAgD,IAAI,CAAC;QAEvE,IAAI,IAAI,CAAC,wBAAwB,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,IAAI,eAAe,CAAC,cAAc,EAAE,CAAC;YAC/F,IAAI,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACrE,YAAY,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACnF,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE;oBAC3B,QAAQ,EAAE,YAAY,CAAC,WAAW;oBAClC,UAAU,EAAE,YAAY,CAAC,UAAU;oBACnC,UAAU,EAAE,YAAY,CAAC,UAAU;iBACpC,CAAC,CAAC;gBAGH,IAAI,eAAe,CAAC,kBAAkB,EAAE,CAAC;oBACvC,IAAI,CAAC;wBAEH,MAAM,WAAW,GAAsB;4BACrC,eAAe,EAAE,EAAE;4BACnB,cAAc,EAAE,CAAC;4BACjB,eAAe,EAAE,CAAC;yBACnB,CAAC;wBAEF,MAAM,IAAI,GAAG,MAAM,eAAe,CAAC,kBAAkB,CAAC,eAAe,CACnE,IAAI,CAAC,MAAM,EACX,YAAY,EACZ,WAAW,CACZ,CAAC;wBAEF,cAAc,GAAG;4BACf,KAAK,EAAE,IAAI,CAAC,KAAkB;4BAC9B,MAAM,EAAE,IAAI,CAAC,MAAM;yBACpB,CAAC;wBAEF,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;4BACxB,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,MAAM,EAAE,IAAI,CAAC,MAAM;yBACpB,CAAC,CAAC;oBACL,CAAC;oBAAC,OAAO,SAAS,EAAE,CAAC;wBACnB,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;wBAC9C,cAAc,GAAG;4BACf,KAAK,EAAE,iCAAS,CAAC,GAAG;4BACpB,MAAM,EAAE,cAAc;yBACvB,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;gBACtD,YAAY,GAAG;oBACb,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,IAAI;oBACd,IAAI,EAAE,IAAI;oBACV,GAAG,EAAE,IAAI;oBACT,UAAU,EAAE,UAAU;oBACtB,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE,CAAC;oBACb,WAAW,EAAE,MAAM;iBACpB,CAAC;YACJ,CAAC;QACH,CAAC;QAGD,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;gBACxB,UAAU,EAAE,CAAC,CAAC,eAAe,CAAC,mBAAmB;gBACjD,WAAW,EAAE,eAAe,CAAC,mBAAmB,EAAE,WAAW,EAAE,IAAI;aACpE,CAAC,CAAC;YAEH,IAAI,eAAe,CAAC,mBAAmB,EAAE,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;gBAGpC,MAAM,eAAe,GAAG;oBACtB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,SAAS,EAAE,IAAI,CAAC,SAAsB;oBACtC,WAAW,EAAE,mCAAW,CAAC,OAAO;oBAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;oBAEzB,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;oBAC7D,OAAO,EAAE,YAAY,EAAE,OAAO,IAAI,IAAI;oBACtC,QAAQ,EAAE,YAAY,EAAE,QAAQ;oBAChC,IAAI,EAAE,YAAY,EAAE,IAAI;oBACxB,GAAG,EAAE,YAAY,EAAE,GAAG;oBACtB,cAAc,EAAE,YAAY,EAAE,UAAU,IAAI,WAAW;oBACvD,WAAW,EAAE,YAAY,EAAE,UAAU,IAAI,GAAG;oBAE5C,SAAS,EAAE,cAAc,EAAE,KAAK,IAAI,iCAAS,CAAC,GAAG;oBACjD,UAAU,EAAE,cAAc,EAAE,MAAM;iBACnC,CAAC;gBAEF,MAAM,eAAe,CAAC,mBAAmB,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gBAEvE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;oBAC7B,QAAQ,EAAE,YAAY,EAAE,WAAW,IAAI,IAAI;oBAC3C,SAAS,EAAE,cAAc,EAAE,KAAK,IAAI,KAAK;oBACzC,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACrC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,IAQ3B;QACC,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,cAAc;YACrB,GAAG,IAAI;YACP,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM;SAC9E,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAGlE,IAAI,YAAY,GAAmC,IAAI,CAAC;QAExD,IAAI,IAAI,CAAC,wBAAwB,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,IAAI,eAAe,CAAC,cAAc,EAAE,CAAC;YAC/F,IAAI,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACvE,YAAY,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAGnF,IAAI,YAAY,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACvE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE;wBAC5B,EAAE,EAAE,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;wBACzC,QAAQ,EAAE,YAAY,CAAC,WAAW;wBAClC,UAAU,EAAE,IAAI,CAAC,UAAU;qBAC5B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;gBAC1D,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC;QACH,CAAC;QAGD,IAAI,CAAC;YACH,IAAI,eAAe,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAEvD,MAAM,eAAe,GAAG;oBACtB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,SAAS,EAAE,IAAI,CAAC,SAAsB;oBACtC,WAAW,EAAE,mCAAW,CAAC,MAAM;oBAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAE3B,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;oBAC7D,OAAO,EAAE,YAAY,EAAE,OAAO,IAAI,IAAI;oBACtC,QAAQ,EAAE,YAAY,EAAE,QAAQ;oBAChC,IAAI,EAAE,YAAY,EAAE,IAAI;oBACxB,GAAG,EAAE,YAAY,EAAE,GAAG;oBACtB,cAAc,EAAE,YAAY,EAAE,UAAU,IAAI,WAAW;oBACvD,WAAW,EAAE,YAAY,EAAE,UAAU,IAAI,GAAG;oBAE5C,SAAS,EAAE,iCAAS,CAAC,GAAG;oBACxB,UAAU,EAAE,MAAM;iBACnB,CAAC;gBAEF,MAAM,eAAe,CAAC,mBAAmB,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gBACvE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAMtB;QACC,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,QAAQ;YACf,GAAG,IAAI;SACR,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAG7D,IAAI,CAAC;YACH,IAAI,eAAe,CAAC,mBAAmB,EAAE,CAAC;gBACxC,MAAM,eAAe,CAAC,mBAAmB,CAAC,YAAY,CACpD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,UAAU,CAChB,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAKtB;QACC,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,eAAe;YACtB,GAAG,IAAI;YACP,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM;SAC9E,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,IAMvB;QACC,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,gBAAgB;YACvB,GAAG,IAAI;YACP,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM;SAC9E,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,IAMvB;QACC,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,gBAAgB;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC;IAKD,MAAM,CAAC,aAAa,CAAC,SAAiB;QACpC,IAAI,CAAC,SAAS;YAAE,OAAO,MAAM,CAAC;QAE9B,IAAI,MAAM,GAAG,MAAM,CAAC;QACpB,IAAI,OAAO,GAAG,OAAO,CAAC;QACtB,IAAI,EAAE,GAAG,MAAM,CAAC;QAGhB,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,EAAE,GAAG,SAAS,CAAC;aAC7C,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,EAAE,GAAG,OAAO,CAAC;aAC/C,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,EAAE,GAAG,OAAO,CAAC;aAC9C,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,EAAE,GAAG,SAAS,CAAC;aAClD,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,EAAE,GAAG,KAAK,CAAC;QAG/C,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,QAAQ,CAAC;aAChD,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,GAAG,SAAS,CAAC;aACvD,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,QAAQ,CAAC;aACrD,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,GAAG,MAAM,CAAC;QAGtD,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,MAAM,GAAG,MAAM,CAAC;aAC7C,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,MAAM,GAAG,MAAM,CAAC;;YAClD,MAAM,GAAG,MAAM,CAAC;QAErB,OAAO,GAAG,MAAM,MAAM,EAAE,MAAM,OAAO,EAAE,CAAC;IAC1C,CAAC;IAKD,MAAM,CAAC,cAAc,CAAC,EAAU;QAK9B,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/E,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,IAAI,EAAE,KAAK,WAAW,IAAI,EAAE,KAAK,WAAW,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;QAOD,OAAO,KAAK,CAAC;IACf,CAAC;IAOD,MAAM,CAAC,MAAM,CAAC,EAAU;QACtB,IAAI,CAAC,EAAE;YAAE,OAAO,MAAM,CAAC;QAEvB,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAErB,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACtB,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAC/C,CAAC;YACD,OAAO,qBAAqB,CAAC;QAC/B,CAAC;aAAM,CAAC;YAEN,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;YACnD,CAAC;YACD,OAAO,gBAAgB,CAAC;QAC1B,CAAC;IACH,CAAC;CACF;AA/ZD,0CA+ZC"}