import { LoginType, LoginStatus, RiskLevel } from '../entities/user_login_log.entity';
export declare class QueryUserLoginLogDto {
    userId?: number;
    loginType?: LoginType;
    loginStatus?: LoginStatus;
    clientIp?: string;
    country?: string;
    province?: string;
    city?: string;
    isp?: string;
    riskLevel?: RiskLevel;
    locationSource?: string;
    startTime?: string;
    endTime?: string;
    page?: number;
    limit?: number;
}
