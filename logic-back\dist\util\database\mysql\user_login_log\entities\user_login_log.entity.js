"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLoginLog = exports.RiskLevel = exports.LogoutType = exports.LoginStatus = exports.LoginType = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
var LoginType;
(function (LoginType) {
    LoginType["PASSWORD"] = "password";
    LoginType["SMS"] = "sms";
    LoginType["QRCODE"] = "qrcode";
    LoginType["REFRESH"] = "refresh";
})(LoginType || (exports.LoginType = LoginType = {}));
var LoginStatus;
(function (LoginStatus) {
    LoginStatus["SUCCESS"] = "success";
    LoginStatus["FAILED"] = "failed";
    LoginStatus["LOGOUT"] = "logout";
})(LoginStatus || (exports.LoginStatus = LoginStatus = {}));
var LogoutType;
(function (LogoutType) {
    LogoutType["ACTIVE"] = "active";
    LogoutType["PASSIVE"] = "passive";
    LogoutType["SWITCH"] = "switch";
})(LogoutType || (exports.LogoutType = LogoutType = {}));
var RiskLevel;
(function (RiskLevel) {
    RiskLevel["LOW"] = "LOW";
    RiskLevel["MEDIUM"] = "MEDIUM";
    RiskLevel["HIGH"] = "HIGH";
})(RiskLevel || (exports.RiskLevel = RiskLevel = {}));
let UserLoginLog = class UserLoginLog {
    id;
    userId;
    loginType;
    loginStatus;
    clientIp;
    userAgent;
    deviceInfo;
    location;
    country;
    province;
    city;
    isp;
    riskLevel;
    riskReason;
    locationSource;
    dataQuality;
    sessionId;
    tokenExpireTime;
    failReason;
    loginTime;
    logoutTime;
    logoutType;
    duration;
    createTime;
    updateTime;
};
exports.UserLoginLog = UserLoginLog;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'bigint', comment: '主键ID' }),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], UserLoginLog.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint', comment: '用户ID' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], UserLoginLog.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 20,
        comment: '登录方式：password-密码登录, sms-短信登录, qrcode-扫码登录, refresh-token刷新'
    }),
    (0, swagger_1.ApiProperty)({ description: '登录方式', enum: ['password', 'sms', 'qrcode', 'refresh'] }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "loginType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 20,
        comment: '登录状态：success-成功, failed-失败, logout-登出'
    }),
    (0, swagger_1.ApiProperty)({ description: '登录状态', enum: ['success', 'failed', 'logout'] }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "loginStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 45,
        comment: '客户端IP地址（支持IPv6）'
    }),
    (0, swagger_1.ApiProperty)({ description: '客户端IP地址' }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "clientIp", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '用户代理信息（浏览器、设备等）'
    }),
    (0, swagger_1.ApiProperty)({ description: '用户代理信息', required: false }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 500,
        nullable: true,
        comment: '设备信息（操作系统、浏览器版本等）'
    }),
    (0, swagger_1.ApiProperty)({ description: '设备信息', required: false }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "deviceInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 200,
        nullable: true,
        comment: 'IP地址对应的地理位置（原始字段，保留兼容性）'
    }),
    (0, swagger_1.ApiProperty)({ description: 'IP地址对应的地理位置', required: false }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 20,
        default: '中国',
        comment: '国家'
    }),
    (0, swagger_1.ApiProperty)({ description: '国家', default: '中国' }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "country", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 30,
        nullable: true,
        comment: '省份'
    }),
    (0, swagger_1.ApiProperty)({ description: '省份', required: false }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "province", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 30,
        nullable: true,
        comment: '城市'
    }),
    (0, swagger_1.ApiProperty)({ description: '城市', required: false }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "city", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: true,
        comment: '网络运营商'
    }),
    (0, swagger_1.ApiProperty)({ description: '网络运营商', required: false }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "isp", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: RiskLevel,
        default: RiskLevel.LOW,
        comment: '风险等级'
    }),
    (0, swagger_1.ApiProperty)({
        description: '风险等级',
        enum: RiskLevel,
        default: RiskLevel.LOW
    }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "riskLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        nullable: true,
        comment: '风险原因'
    }),
    (0, swagger_1.ApiProperty)({ description: '风险原因', required: false }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "riskReason", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 20,
        default: 'ip2region',
        comment: '位置数据来源'
    }),
    (0, swagger_1.ApiProperty)({ description: '位置数据来源', default: 'ip2region' }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "locationSource", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 100,
        comment: '数据质量评分(0-100)'
    }),
    (0, swagger_1.ApiProperty)({ description: '数据质量评分(0-100)', default: 100 }),
    __metadata("design:type", Number)
], UserLoginLog.prototype, "dataQuality", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        nullable: true,
        comment: '会话ID'
    }),
    (0, swagger_1.ApiProperty)({ description: '会话ID', required: false }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "sessionId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'datetime',
        nullable: true,
        comment: 'Token过期时间'
    }),
    (0, swagger_1.ApiProperty)({ description: 'Token过期时间', required: false }),
    __metadata("design:type", Date)
], UserLoginLog.prototype, "tokenExpireTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 200,
        nullable: true,
        comment: '登录失败原因'
    }),
    (0, swagger_1.ApiProperty)({ description: '登录失败原因', required: false }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "failReason", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'datetime',
        default: () => 'CURRENT_TIMESTAMP',
        comment: '登录时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '登录时间' }),
    __metadata("design:type", Date)
], UserLoginLog.prototype, "loginTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'datetime',
        nullable: true,
        comment: '登出时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '登出时间', required: false }),
    __metadata("design:type", Date)
], UserLoginLog.prototype, "logoutTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'passive', 'switch'],
        nullable: true,
        comment: '登出类型：active-主动登出, passive-被动登出, switch-切换登出'
    }),
    (0, swagger_1.ApiProperty)({
        description: '登出类型',
        enum: ['active', 'passive', 'switch'],
        required: false
    }),
    __metadata("design:type", String)
], UserLoginLog.prototype, "logoutType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'int',
        nullable: true,
        comment: '会话持续时间（秒）'
    }),
    (0, swagger_1.ApiProperty)({ description: '会话持续时间（秒）', required: false }),
    __metadata("design:type", Number)
], UserLoginLog.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        type: 'datetime',
        comment: '创建时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], UserLoginLog.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        type: 'datetime',
        comment: '更新时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], UserLoginLog.prototype, "updateTime", void 0);
exports.UserLoginLog = UserLoginLog = __decorate([
    (0, typeorm_1.Entity)('user_login_log'),
    (0, typeorm_1.Index)(['userId']),
    (0, typeorm_1.Index)(['loginTime']),
    (0, typeorm_1.Index)(['clientIp']),
    (0, typeorm_1.Index)(['loginStatus']),
    (0, typeorm_1.Index)(['loginType']),
    (0, typeorm_1.Index)('idx_user_location', ['userId', 'province', 'city']),
    (0, typeorm_1.Index)('idx_risk_level', ['riskLevel']),
    (0, typeorm_1.Index)('idx_location_source', ['locationSource'])
], UserLoginLog);
//# sourceMappingURL=user_login_log.entity.js.map