import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, IsNull } from 'typeorm';
import { UserLoginLog, LoginType, LoginStatus, RiskLevel } from './entities/user_login_log.entity';
import { CreateUserLoginLogDto } from './dto/create-user-login-log.dto';
import { QueryUserLoginLogDto } from './dto/query-user-login-log.dto';

export interface LoginLogData {
  userId: number;
  loginType: LoginType;
  loginStatus: LoginStatus;
  clientIp: string;
  userAgent?: string;
  deviceInfo?: string;
  location?: string;
  sessionId?: string;
  tokenExpireTime?: Date;
  failReason?: string;
  // 新增：IP地理位置字段
  country?: string;
  province?: string;
  city?: string;
  isp?: string;
  riskLevel?: RiskLevel;
  riskReason?: string;
  locationSource?: string;
  dataQuality?: number;
}

@Injectable()
export class UserLoginLogService {
  constructor(
    @InjectRepository(UserLoginLog)
    private readonly userLoginLogRepository: Repository<UserLoginLog>,
  ) {}

  /**
   * 记录登录日志
   */
  async recordLogin(data: LoginLogData): Promise<UserLoginLog> {
    console.log('🚀 开始记录登录日志，输入数据:', JSON.stringify(data, null, 2));

    try {
      const loginLog = this.userLoginLogRepository.create({
        ...data,
        loginTime: new Date(),
      });

      console.log('📝 创建的登录日志实体:', JSON.stringify(loginLog, null, 2));

      console.log('💾 开始保存到数据库...');
      const savedLog = await this.userLoginLogRepository.save(loginLog);

      console.log('✅ 登录日志保存成功，ID:', savedLog.id);
      return savedLog;
    } catch (error) {
      console.error('❌ 保存登录日志到数据库失败:', error);
      console.error('❌ 错误详情:', error.stack);
      throw error;
    }
  }

  /**
   * 记录登出日志
   */
  async recordLogout(
    userId: number,
    sessionId?: string,
    reason?: string,
    logoutType?: 'active' | 'passive' | 'switch'
  ): Promise<void> {
    console.log('🚪 开始记录登出，参数:', { userId, sessionId, reason, logoutType });

    // 先查看该用户的所有登录记录
    const allUserLogs = await this.userLoginLogRepository.find({
      where: { userId },
      order: { loginTime: 'DESC' },
      take: 5
    });

    console.log('📊 该用户最近5条登录记录:', allUserLogs.map(log => ({
      id: log.id,
      loginType: log.loginType,
      loginStatus: log.loginStatus,
      sessionId: log.sessionId,
      loginTime: log.loginTime,
      logoutTime: log.logoutTime
    })));

    if (sessionId) {
      // 通过sessionId查找最近的成功登录记录
      console.log('🔍 通过sessionId查找登录记录...');
      const loginLog = await this.userLoginLogRepository.findOne({
        where: {
          userId,
          sessionId,
          loginStatus: LoginStatus.SUCCESS,
          logoutTime: IsNull()
        },
        order: { loginTime: 'DESC' }
      });

      console.log('📋 查找到的登录记录:', loginLog ? {
        id: loginLog.id,
        userId: loginLog.userId,
        sessionId: loginLog.sessionId,
        loginTime: loginLog.loginTime,
        loginStatus: loginLog.loginStatus
      } : '未找到记录');

      if (loginLog) {
        const logoutTime = new Date();
        const duration = Math.floor((logoutTime.getTime() - loginLog.loginTime.getTime()) / 1000);

        console.log('💾 开始更新登出信息...');
        await this.userLoginLogRepository.update(loginLog.id, {
          logoutTime,
          duration,
          logoutType: logoutType || 'active' // 默认为主动登出
        });

        console.log('✅ 登出日志更新成功:', {
          userId,
          sessionId,
          duration: `${duration}秒`,
          logoutTime
        });
      } else {
        console.warn('⚠️ 未找到对应的登录记录，无法更新登出时间');
      }
    } else {
      // 如果没有sessionId，更新该用户最近的成功登录记录
      console.log('🔍 没有sessionId，查找用户最近的登录记录...');
      const loginLog = await this.userLoginLogRepository.findOne({
        where: {
          userId,
          loginStatus: LoginStatus.SUCCESS,
          logoutTime: IsNull()
        },
        order: { loginTime: 'DESC' }
      });

      console.log('📋 查找到的最近登录记录:', loginLog ? {
        id: loginLog.id,
        userId: loginLog.userId,
        sessionId: loginLog.sessionId,
        loginTime: loginLog.loginTime,
        loginStatus: loginLog.loginStatus
      } : '未找到记录');

      if (loginLog) {
        const logoutTime = new Date();
        const duration = Math.floor((logoutTime.getTime() - loginLog.loginTime.getTime()) / 1000);

        console.log('💾 开始更新最近登录记录的登出信息...');
        await this.userLoginLogRepository.update(loginLog.id, {
          logoutTime,
          duration,
          logoutType: logoutType || 'active' // 默认为主动登出
        });

        console.log('✅ 最近登录记录的登出日志更新成功:', {
          userId,
          duration: `${duration}秒`,
          logoutTime
        });
      } else {
        console.warn('⚠️ 未找到该用户的未登出记录，无法更新登出时间');
      }
    }
  }

  /**
   * 获取用户登录历史
   */
  async getUserLoginHistory(userId: number, limit: number = 10): Promise<UserLoginLog[]> {
    return await this.userLoginLogRepository.find({
      where: { userId },
      order: { loginTime: 'DESC' },
      take: limit
    });
  }

  /**
   * 获取用户最近的登录记录
   */
  async getLastLogin(userId: number): Promise<UserLoginLog | null> {
    return await this.userLoginLogRepository.findOne({
      where: { 
        userId, 
        loginStatus: LoginStatus.SUCCESS 
      },
      order: { loginTime: 'DESC' }
    });
  }

  /**
   * 分页查询登录日志
   */
  async findWithPagination(query: QueryUserLoginLogDto): Promise<{
    data: UserLoginLog[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { page = 1, limit = 10, startTime, endTime, ...filters } = query;
    
    const queryBuilder = this.userLoginLogRepository.createQueryBuilder('log');
    
    // 添加过滤条件
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined) {
        queryBuilder.andWhere(`log.${key} = :${key}`, { [key]: filters[key] });
      }
    });
    
    // 添加时间范围过滤
    if (startTime && endTime) {
      queryBuilder.andWhere('log.loginTime BETWEEN :startTime AND :endTime', {
        startTime,
        endTime
      });
    } else if (startTime) {
      queryBuilder.andWhere('log.loginTime >= :startTime', { startTime });
    } else if (endTime) {
      queryBuilder.andWhere('log.loginTime <= :endTime', { endTime });
    }
    
    // 分页和排序
    const [data, total] = await queryBuilder
      .orderBy('log.loginTime', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();
    
    return {
      data,
      total,
      page,
      limit
    };
  }

  /**
   * 检查异常登录（不同IP或设备）
   */
  async checkAbnormalLogin(userId: number, clientIp: string, userAgent: string): Promise<boolean> {
    // 获取用户最近7天的登录记录
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentLogins = await this.userLoginLogRepository.find({
      where: {
        userId,
        loginStatus: LoginStatus.SUCCESS,
        loginTime: Between(sevenDaysAgo, new Date())
      },
      order: { loginTime: 'DESC' },
      take: 20
    });

    // 检查是否为新IP
    const hasUsedThisIp = recentLogins.some(log => log.clientIp === clientIp);
    
    // 检查是否为新设备（简单的User-Agent比较）
    const hasUsedSimilarDevice = recentLogins.some(log => {
      if (!log.userAgent) return false;
      // 简单比较，实际可以更复杂
      return log.userAgent.includes(this.extractBrowserInfo(userAgent));
    });

    return !hasUsedThisIp || !hasUsedSimilarDevice;
  }

  /**
   * 提取浏览器信息（简单实现）
   */
  private extractBrowserInfo(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  /**
   * 获取登录统计信息
   */
  async getLoginStats(userId: number, days: number = 30): Promise<any> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const stats = await this.userLoginLogRepository
      .createQueryBuilder('log')
      .select([
        'COUNT(*) as totalLogins',
        'COUNT(CASE WHEN login_status = "success" THEN 1 END) as successLogins',
        'COUNT(CASE WHEN login_status = "failed" THEN 1 END) as failedLogins',
        'COUNT(DISTINCT client_ip) as uniqueIps',
        'AVG(duration) as avgDuration'
      ])
      .where('log.userId = :userId', { userId })
      .andWhere('log.loginTime >= :startDate', { startDate })
      .getRawOne();

    return stats;
  }

  /**
   * 创建登录日志
   */
  async create(createDto: CreateUserLoginLogDto): Promise<UserLoginLog> {
    const loginLog = this.userLoginLogRepository.create(createDto);
    return await this.userLoginLogRepository.save(loginLog);
  }

  /**
   * 根据ID查找登录日志
   */
  async findOne(id: number): Promise<UserLoginLog | null> {
    return await this.userLoginLogRepository.findOne({ where: { id } });
  }

  /**
   * 删除登录日志
   */
  async remove(id: number): Promise<void> {
    await this.userLoginLogRepository.delete(id);
  }
}
