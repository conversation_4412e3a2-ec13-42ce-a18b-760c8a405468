export declare enum LoginType {
    PASSWORD = "password",
    SMS = "sms",
    QRCODE = "qrcode",
    REFRESH = "refresh"
}
export declare enum LoginStatus {
    SUCCESS = "success",
    FAILED = "failed",
    LOGOUT = "logout"
}
export declare enum LogoutType {
    ACTIVE = "active",
    PASSIVE = "passive",
    SWITCH = "switch"
}
export declare enum RiskLevel {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH"
}
export declare class UserLoginLog {
    id: number;
    userId: number;
    loginType: string;
    loginStatus: string;
    clientIp: string;
    userAgent?: string;
    deviceInfo?: string;
    location?: string;
    country: string;
    province?: string;
    city?: string;
    isp?: string;
    riskLevel: RiskLevel;
    riskReason?: string;
    locationSource: string;
    dataQuality: number;
    sessionId?: string;
    tokenExpireTime?: Date;
    failReason?: string;
    loginTime: Date;
    logoutTime?: Date;
    logoutType?: 'active' | 'passive' | 'switch';
    duration?: number;
    createTime: Date;
    updateTime: Date;
}
