import { Modu<PERSON> } from '@nestjs/common';
import { UserLoginLogModule as DatabaseUserLoginLogModule } from '../../util/database/mysql/user_login_log/user_login_log.module';
import { IpLocationModule } from '../../util/ip_location/ip-location.module';
import { LoginLoggerInitializerService } from './login-logger-initializer.service';

@Module({
  imports: [
    DatabaseUserLoginLogModule,
    IpLocationModule  // 导入IP地理位置模块
  ],
  providers: [LoginLoggerInitializerService],
  exports: [LoginLoggerInitializerService]
})
export class UserLoginLogModule {}
