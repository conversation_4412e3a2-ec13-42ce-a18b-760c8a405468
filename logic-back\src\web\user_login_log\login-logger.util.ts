import { UserLoginLogService } from '../../util/database/mysql/user_login_log/user_login_log.service';
import { LoginType, LoginStatus, RiskLevel } from '../../util/database/mysql/user_login_log/entities/user_login_log.entity';
import { IpLocationUtil, LocationInfoWithQuality } from '../../util/ip_location/utils/ip-location.util';
import { RiskAssessmentUtil, UserLocationStats } from '../../util/ip_location/utils/risk-assessment.util';

/**
 * 登录日志工具类 - 集成数据库存储和IP地理位置解析
 */
export class LoginLoggerUtil {
  private static userLoginLogService: UserLoginLogService;
  private static ipLocationUtil: IpLocationUtil;
  private static riskAssessmentUtil: RiskAssessmentUtil;

  // 设置服务实例（在模块初始化时调用）
  static setService(service: UserLoginLogService) {
    console.log('🔧 设置 LoginLoggerUtil 服务实例:', {
      hasService: !!service,
      serviceType: service?.constructor?.name
    });
    LoginLoggerUtil.userLoginLogService = service;
    console.log('✅ LoginLoggerUtil 服务实例设置完成');
  }

  // 设置IP地理位置服务实例
  static setIpLocationService(ipLocationUtil: IpLocationUtil, riskAssessmentUtil: RiskAssessmentUtil) {
    console.log('🌍 设置 IP地理位置服务实例:', {
      hasIpLocationUtil: !!ipLocationUtil,
      hasRiskAssessmentUtil: !!riskAssessmentUtil
    });
    LoginLoggerUtil.ipLocationUtil = ipLocationUtil;
    LoginLoggerUtil.riskAssessmentUtil = riskAssessmentUtil;
    console.log('✅ IP地理位置服务实例设置完成');
  }

  /**
   * 记录成功登录（集成IP地理位置解析）
   */
  static async logSuccessLogin(data: {
    userId: number;
    loginType: LoginType;
    clientIp: string;
    userAgent?: string;
    deviceInfo?: string;
    sessionId?: string;
    enableLocationResolution?: boolean; // 是否启用地理位置解析，默认true
  }) {
    const startTime = Date.now();
    const logData = {
      timestamp: new Date().toISOString(),
      level: 'INFO',
      event: 'LOGIN_SUCCESS',
      ...data,
      userAgent: data.userAgent ? `${data.userAgent.substring(0, 100)}...` : '未知设备'
    };

    console.log('✅ [LOGIN_SUCCESS]', JSON.stringify(logData, null, 2));

    // 解析IP地理位置信息
    let locationInfo: LocationInfoWithQuality | null = null;
    let riskAssessment: { level: RiskLevel; reason: string } | null = null;

    if (data.enableLocationResolution !== false && data.clientIp && LoginLoggerUtil.ipLocationUtil) {
      try {
        console.log('🌍 开始解析IP地理位置:', LoginLoggerUtil.maskIP(data.clientIp));
        locationInfo = await LoginLoggerUtil.ipLocationUtil.getLocationByIP(data.clientIp);
        console.log('✅ IP地理位置解析完成:', {
          location: locationInfo.displayName,
          confidence: locationInfo.confidence,
          dataSource: locationInfo.dataSource
        });

        // 进行风险评估（需要用户历史数据，这里先使用简化版本）
        if (LoginLoggerUtil.riskAssessmentUtil) {
          try {
            // 简化的用户历史数据（实际应该从数据库查询）
            const userHistory: UserLocationStats = {
              commonLocations: [], // 实际应该查询用户常用登录地
              riskLoginCount: 0,
              totalLoginCount: 1
            };

            const risk = await LoginLoggerUtil.riskAssessmentUtil.assessLoginRisk(
              data.userId,
              locationInfo,
              userHistory
            );

            riskAssessment = {
              level: risk.level as RiskLevel,
              reason: risk.reason
            };

            console.log('🔍 风险评估完成:', {
              level: risk.level,
              score: risk.score,
              reason: risk.reason
            });
          } catch (riskError) {
            console.warn('⚠️ 风险评估失败:', riskError.message);
            riskAssessment = {
              level: RiskLevel.LOW,
              reason: '风险评估异常，默认低风险'
            };
          }
        }
      } catch (locationError) {
        console.warn('⚠️ IP地理位置解析失败:', locationError.message);
        locationInfo = {
          country: '未知',
          province: '未知',
          city: '未知',
          isp: '未知',
          dataSource: 'fallback',
          hasEmptyFields: true,
          confidence: 0,
          displayName: '未知位置'
        };
      }
    }

    // 存储到数据库
    try {
      console.log('🔍 检查服务实例:', {
        hasService: !!LoginLoggerUtil.userLoginLogService,
        serviceType: LoginLoggerUtil.userLoginLogService?.constructor?.name
      });

      if (LoginLoggerUtil.userLoginLogService) {
        console.log('📝 开始保存登录成功日志到数据库...');

        // 构建完整的登录日志数据
        const completeLogData = {
          userId: data.userId,
          loginType: data.loginType as LoginType,
          loginStatus: LoginStatus.SUCCESS,
          clientIp: data.clientIp,
          userAgent: data.userAgent,
          deviceInfo: data.deviceInfo,
          sessionId: data.sessionId,
          // 地理位置信息
          location: locationInfo ? locationInfo.displayName : undefined,
          country: locationInfo?.country || '中国',
          province: locationInfo?.province,
          city: locationInfo?.city,
          isp: locationInfo?.isp,
          locationSource: locationInfo?.dataSource || 'ip2region',
          dataQuality: locationInfo?.confidence || 100,
          // 风险评估信息
          riskLevel: riskAssessment?.level || RiskLevel.LOW,
          riskReason: riskAssessment?.reason
        };

        await LoginLoggerUtil.userLoginLogService.recordLogin(completeLogData);

        console.log('✅ 登录成功日志已保存到数据库', {
          location: locationInfo?.displayName || '未知',
          riskLevel: riskAssessment?.level || 'LOW',
          responseTime: Date.now() - startTime
        });
      } else {
        console.warn('⚠️ UserLoginLogService 服务实例未初始化，无法保存到数据库');
      }
    } catch (error) {
      console.error('❌ 保存登录成功日志到数据库失败:', error);
      console.error('❌ 错误详情:', error.stack);
    }
  }

  /**
   * 记录登录失败（集成IP地理位置解析）
   */
  static async logFailedLogin(data: {
    userId?: number;
    phone?: string;
    loginType: LoginType;
    clientIp: string;
    userAgent?: string;
    failReason: string;
    enableLocationResolution?: boolean; // 是否启用地理位置解析，默认true
  }) {
    const logData = {
      timestamp: new Date().toISOString(),
      level: 'WARN',
      event: 'LOGIN_FAILED',
      ...data,
      userAgent: data.userAgent ? `${data.userAgent.substring(0, 100)}...` : '未知设备'
    };

    console.log('❌ [LOGIN_FAILED]', JSON.stringify(logData, null, 2));

    // 解析IP地理位置信息（用于安全分析）
    let locationInfo: LocationInfoWithQuality | null = null;

    if (data.enableLocationResolution !== false && data.clientIp && LoginLoggerUtil.ipLocationUtil) {
      try {
        console.log('🌍 解析失败登录IP地理位置:', LoginLoggerUtil.maskIP(data.clientIp));
        locationInfo = await LoginLoggerUtil.ipLocationUtil.getLocationByIP(data.clientIp);

        // 记录可疑的失败登录（来自异常位置的失败尝试）
        if (locationInfo.country !== '中国' || data.failReason?.includes('密码错误')) {
          console.log('🚨 检测到可疑的失败登录:', {
            ip: LoginLoggerUtil.maskIP(data.clientIp),
            location: locationInfo.displayName,
            failReason: data.failReason
          });
        }
      } catch (locationError) {
        console.warn('⚠️ 失败登录IP地理位置解析失败:', locationError.message);
        locationInfo = null;
      }
    }

    // 存储到数据库
    try {
      if (LoginLoggerUtil.userLoginLogService && data.userId) {
        // 构建完整的失败登录日志数据
        const completeLogData = {
          userId: data.userId,
          loginType: data.loginType as LoginType,
          loginStatus: LoginStatus.FAILED,
          clientIp: data.clientIp,
          userAgent: data.userAgent,
          failReason: data.failReason,
          // 地理位置信息（如果解析成功）
          location: locationInfo ? locationInfo.displayName : undefined,
          country: locationInfo?.country || '中国',
          province: locationInfo?.province,
          city: locationInfo?.city,
          isp: locationInfo?.isp,
          locationSource: locationInfo?.dataSource || 'ip2region',
          dataQuality: locationInfo?.confidence || 100,
          // 失败登录默认为低风险，但记录位置信息用于后续分析
          riskLevel: RiskLevel.LOW,
          riskReason: '登录失败'
        };

        await LoginLoggerUtil.userLoginLogService.recordLogin(completeLogData);
        console.log('✅ 登录失败日志已保存到数据库');
      }
    } catch (error) {
      console.error('❌ 保存登录失败日志到数据库失败:', error);
    }
  }

  /**
   * 记录登出
   */
  static async logLogout(data: {
    userId: number;
    clientIp: string;
    sessionId?: string;
    reason?: string;
    logoutType?: 'active' | 'passive' | 'switch';
  }) {
    const logData = {
      timestamp: new Date().toISOString(),
      level: 'INFO',
      event: 'LOGOUT',
      ...data
    };

    console.log('🚪 [LOGOUT]', JSON.stringify(logData, null, 2));

    // 更新数据库中的登出时间
    try {
      if (LoginLoggerUtil.userLoginLogService) {
        await LoginLoggerUtil.userLoginLogService.recordLogout(
          data.userId,
          data.sessionId,
          data.reason,
          data.logoutType
        );
      }
    } catch (error) {
      console.error('❌ 保存登出日志到数据库失败:', error);
    }
  }

  /**
   * 记录Token刷新
   */
  static logTokenRefresh(data: {
    userId: number;
    clientIp: string;
    userAgent?: string;
    oldTokenExpired?: boolean;
  }) {
    const logData = {
      timestamp: new Date().toISOString(),
      level: 'INFO',
      event: 'TOKEN_REFRESH',
      ...data,
      userAgent: data.userAgent ? `${data.userAgent.substring(0, 100)}...` : '未知设备'
    };

    console.log('🔄 [TOKEN_REFRESH]', JSON.stringify(logData, null, 2));
  }

  /**
   * 记录异常登录检测
   */
  static logAbnormalLogin(data: {
    userId: number;
    clientIp: string;
    userAgent?: string;
    reason: string;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  }) {
    const logData = {
      timestamp: new Date().toISOString(),
      level: 'WARN',
      event: 'ABNORMAL_LOGIN',
      ...data,
      userAgent: data.userAgent ? `${data.userAgent.substring(0, 100)}...` : '未知设备'
    };

    console.log('⚠️ [ABNORMAL_LOGIN]', JSON.stringify(logData, null, 2));
  }

  /**
   * 记录安全事件
   */
  static logSecurityEvent(data: {
    userId?: number;
    clientIp: string;
    eventType: 'BRUTE_FORCE' | 'SUSPICIOUS_IP' | 'MULTIPLE_DEVICE' | 'TOKEN_HIJACK';
    description: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  }) {
    const logData = {
      timestamp: new Date().toISOString(),
      level: 'ERROR',
      event: 'SECURITY_EVENT',
      eventType: data.eventType,
      userId: data.userId,
      clientIp: data.clientIp,
      description: data.description,
      severity: data.severity
    };

    console.log('🚨 [SECURITY_EVENT]', JSON.stringify(logData, null, 2));
  }

  /**
   * 获取设备信息摘要
   */
  static getDeviceInfo(userAgent: string): string {
    if (!userAgent) return '未知设备';
    
    let device = '未知设备';
    let browser = '未知浏览器';
    let os = '未知系统';

    // 检测操作系统
    if (userAgent.includes('Windows')) os = 'Windows';
    else if (userAgent.includes('Mac OS')) os = 'macOS';
    else if (userAgent.includes('Linux')) os = 'Linux';
    else if (userAgent.includes('Android')) os = 'Android';
    else if (userAgent.includes('iOS')) os = 'iOS';

    // 检测浏览器
    if (userAgent.includes('Chrome')) browser = 'Chrome';
    else if (userAgent.includes('Firefox')) browser = 'Firefox';
    else if (userAgent.includes('Safari')) browser = 'Safari';
    else if (userAgent.includes('Edge')) browser = 'Edge';

    // 检测设备类型
    if (userAgent.includes('Mobile')) device = '移动设备';
    else if (userAgent.includes('Tablet')) device = '平板设备';
    else device = '桌面设备';

    return `${device} - ${os} - ${browser}`;
  }

  /**
   * 检查IP是否可疑
   */
  static isSuspiciousIp(ip: string): boolean {
    // 简单的可疑IP检测逻辑
    // 实际项目中可以集成IP威胁情报库
    
    // 检查是否为内网IP（开发环境常见）
    if (ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
      return false;
    }
    
    // 检查是否为本地IP
    if (ip === '127.0.0.1' || ip === 'localhost') {
      return false;
    }
    
    // TODO: 可以添加更多检测逻辑
    // - 检查IP地理位置变化
    // - 检查IP黑名单
    // - 检查代理/VPN
    
    return false;
  }

  /**
   * IP地址脱敏
   * @param ip IP地址
   * @returns 脱敏后的IP地址
   */
  static maskIP(ip: string): string {
    if (!ip) return '未知IP';

    if (ip.includes(':')) {
      // IPv6脱敏
      const parts = ip.split(':');
      if (parts.length >= 4) {
        return `${parts.slice(0, 4).join(':')}:****`;
      }
      return '****:****:****:****';
    } else {
      // IPv4脱敏
      const parts = ip.split('.');
      if (parts.length === 4) {
        return `${parts[0]}.${parts[1]}.${parts[2]}.***`;
      }
      return '***.***.***.**';
    }
  }
}
